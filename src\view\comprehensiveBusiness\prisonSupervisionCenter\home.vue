<template>
    <div>
        <s-DataGrid ref="grid" funcMark="jsdd:list" :customFunc="true" v-if="!showAdd">
            <template slot="customHeadFunc" slot-scope="{ func }">
                <Button type="primary" v-if="func.includes(globalAppCode + ':jsdd:list:add')" icon="md-add"
                    @click.native="handleAdd">新增督导</Button>
            </template>
            <template slot="customRowFunc" slot-scope="{ func,row,index }">
                <Button type="primary" v-if="func.includes(globalAppCode + ':jsdd:list:approve')
                    && row.status == '01'" @click.native="handleApproval(row)">审批</Button>
                <Button type="primary" v-if="func.includes(globalAppCode + ':jsdd:list:appeal') && row.status == '04'"
                    style="margin-left: 10px;">申诉</Button>
                <Button type="primary" v-if="func.includes(globalAppCode + ':jsdd:list:feed') && row.status == '04'"
                    style="margin-left: 10px;">反馈</Button>
                <Button type="primary" v-if="func.includes(globalAppCode + ':jsdd:list:detail') && row.status == '07'"
                    style="margin-left: 10px;">详情</Button>
            </template>
        </s-DataGrid>
        <div v-if="showAdd" style="height: 100%">
            <component :is="component" @on_show_table="on_show_table" :msgDate="msgDate" />
        </div>
    </div>
</template>
<script>
import addSupervision from './addSupervision.vue';
import leaderApprove from './leaderApprove.vue';
export default {
    components: {
        addSupervision,
        leaderApprove
    },
    data() {
        return {
            showAdd: false,
            component: null,
            msgDate: {}
        }
    },
    mounted() {
    },
    methods: {
        handleAdd() {
            this.component = 'addSupervision'
            this.showAdd = true
        },
        on_show_table() {
            this.showAdd = false
            this.component = null
            this.$refs.grid.query_grid_data(1)
        },
        handleApproval(row) {
            console.log(row);
            this.component = 'leaderApprove'
            this.msgDate = row
            this.showAdd = true


        }

    }
}

</script>

<style scoped lang="less">
.com-content-wrapper {
    display: flex;
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid #ebeef5;

}

.com-module-tabs {
    /deep/.ivu-badge-count {
        // min-width: 15px;
        // height: 15px;
        // line-height: 13px;
        // border-radius: 50%;
    }
}
</style>