<template>
    <div>
        <Card title="领导意见" dis-hover :bordered="false">
            <div class="com-form-container">
                <div class="com-module-layout fm-content-info">
                    <p class="detail-title">基本信息</p>
                    <div class="fm-content-box">
                        <Row>
                            <Col span="3"><span>违规时间</span></Col>
                            <Col span="5"><span>{{ formItem.outlineTime }}</span></Col>
                            <Col span="3"><span>违规地点</span></Col>
                            <Col span="13"><span>{{ formItem.addressName }}</span></Col>
                            <Col span="3"><span>巡查来源</span></Col>
                            <Col span="5"><span>{{ formItem.patrolSource }}</span></Col>
                            <Col span="3"><span>违规对象</span></Col>
                            <Col span="5"><span>{{ formItem.outlineObject }}</span></Col>
                            <Col span="3"><span>违规人员</span></Col>
                            <Col span="5"><span>{{ formItem.outlinePeopleName }}</span></Col>
                            <Col span="3"><span>违规类型</span></Col>
                            <Col span="21"><span>{{ formItem.outLineViolation }}</span></Col>
                            <Col span="3"><span>违规详情</span></Col>
                            <Col span="21"><span>{{ formItem.detail }}</span></Col>
                            <Col span="3"><span>巡查时间</span></Col>
                            <Col span="5"><span>{{ formItem.aroundTime }}</span></Col>
                            <Col span="3"><span>整改期限</span></Col>
                            <Col span="13"><span>{{ formItem.correctPeriod }}</span></Col>
                            <Col span="3"><span>备注</span></Col>
                            <Col span="21"><span>{{ formItem.remark }}</span></Col>
                            <Col span="3"><span>附件</span></Col>
                            <Col span="21"><span>
                                <file-upload :defaultList="formItem.attUrl" :serviceMark="serviceMark"
                                    :bucketName="bucketName" :isDetail="true" v-if="formItem.attUrl" />
                            </span></Col>
                            <Col span="3"><span>违规登记人</span></Col>
                            <Col span="5"><span>{{ formItem.operateUserName }}</span></Col>
                            <Col span="3"><span>登记时间</span></Col>
                            <Col span="13"><span>{{ formItem.operateTime }}</span></Col>
                        </Row>
                    </div>
                </div>
                <div>
                    <Form :model="leaderFormItem" ref="leaderForm" :label-width="100">
                        <Row>
                            <Col span="8">
                            <FormItem label="领导批示" prop="leaderIdea">
                                <Select v-model="leaderFormItem.leaderIdea">
                                    <Option value="04">同意</Option>
                                    <Option value="02">不同意</Option>
                                    <Option value="03">退回</Option>
                                </Select>
                            </FormItem>
                            </Col>

                        </Row>
                        <Row>
                            <Col span="8">
                            <FormItem label="审批意见" prop="approvalOpinion">
                                <Input v-model="leaderFormItem.approvalOpinion" placeholder="请输入审批意见" type="textarea"
                                    :autosize="{ minRows: 2, maxRows: 5 }"></Input>
                            </FormItem>
                            </Col>
                        </Row>

                    </Form>
                </div>
                <div class="bsp-base-fotter">
                    <Button @click="on_show_table(false)">返 回</Button>
                    <Button type="primary" style="margin-left: 25px" @click="handleSubmit()">提 交</Button>
                </div>
            </div>

        </Card>

    </div>

</template>
<script>
import { fileUpload } from 'sd-minio-upfile'
import { Row } from 'view-design';
export default {
    name: "leaderApprove",
    props: {
        msgDate: Object,
    },

    components: {
        fileUpload
    },
    // directives: { loading },
    data() {

        return {
            openPrison: false,
            orgCodeList: [],
            formItem: {
            },
            // 文件上传
            showFile: true,
            serviceMark: serverConfig.OSS_SERVICE_MARK,
            bucketName: serverConfig.bucketName,
            leaderFormItem: {
                leaderIdea: '04'
            }

        };
    },
    async created() {
    },
    mounted() {
        this.getInfor();
    },
    methods: {
        on_show_table() {
            this.$emit('on_show_table')
        },
        handleSubmit() {

        },
        getInfor() {
            this.$store.dispatch('authGetRequest', { url: this.$path.zh_superviseGet, params: { id: this.msgDate.id } }).then(resp => {
                if (resp.success) {
                    this.formItem = resp.data
                    this.formItem.attUrl = JSON.parse(this.formItem.attUrl)
                } else {
                    this.$Message.error(resp.message)
                }
            })
        }
    }

};
</script>
